import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { DemoService } from './demo/demo.service';
import { ExampleService } from './example/example.service';
import { ExampleController } from './example/example.controller';
import { DemoController } from './demo/demo.controller';
import { PREFIX_MODULE } from '../config-module';

@ChildModule({
    prefix: PREFIX_MODULE.mock,
    providers: [DemoService, ExampleService],
    controllers: [ExampleController, DemoController],
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
