import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { PREFIX_MODULE } from '../config-module';
import { AdminTenantService } from './tenant/tenant.service';
import { AdminTenantController } from './tenant/tenant.controller';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
@ChildModule({
    prefix: PREFIX_MODULE.admin,
    imports: [PrimaryRepoModule],
    providers: [AdminTenantService],
    controllers: [AdminTenantController],
})
export class AdminModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
