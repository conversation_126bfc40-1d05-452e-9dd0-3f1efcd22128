import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { CreateTenantAppReq, RegisterTenantReq } from './dto';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { TenantRepo, AccountRepo, ApplicationRepo, TenantApplicationRepo } from '~/domains/primary';
import { BusinessException } from '~/@systems/exceptions';
import securityHelper from '~/@core/helpers/security.helper';
import { NSAccount } from '~/common/enums';
import * as crypto from 'crypto';

@Injectable()
export class AdminTenantService {
    constructor(
        private jwtService: JwtService,
        @InjectRepo(TenantRepo)
        readonly tenantRepo: TenantRepo,
        @InjectRepo(AccountRepo)
        readonly accountRepo: AccountRepo,
        @InjectRepo(ApplicationRepo)
        readonly applicationRepo: ApplicationRepo,
        @InjectRepo(TenantApplicationRepo)
        readonly tenantApplicationRepo: TenantApplicationRepo,
    ) {}

    createApplication() {
        const app = this.applicationRepo.save({
            code: 'CRM',
            name: 'Ape tech CRM',
            description: 'CRM',
        });
        return app;
    }

    async register(body: RegisterTenantReq) {
        // 1. Kiểm tra domain đã tồn tại chưa
        const checkTenant = await this.tenantRepo.findOne({ where: { domain: body.domain } });
        if (checkTenant) {
            throw new BusinessException('tenant.register.error.tenant_existed');
        }

        // 2. Tạo mới tenant
        const tenant = await this.tenantRepo.save({
            name: body.name,
            domain: body.domain,
        });

        // 3. Tạo account root
        const account = await this.accountRepo.save({
            tenantId: tenant.id,
            username: body.rootUsername,
            password: await securityHelper.hash(body.rootPassword),
            fullName: body.rootFullName,
            isRoot: true,
            status: NSAccount.EStatus.ACTIVE,
        });
        delete account.password;

        // 4. Mapping applications
        // body.applications: [{ code, redirectUris }]
        const appResults = [];
        if (body.applications && Array.isArray(body.applications)) {
            for (const appReq of body.applications) {
                // Lấy hoặc tạo app gốc
                let app = await this.applicationRepo.findOne({ where: { code: appReq.code } });
                if (!app) {
                    app = await this.applicationRepo.save({ code: appReq.code, name: appReq.code });
                }
                // Sinh clientId/clientSecret
                const clientId = `ape_${tenant.id}_${app.code}_${crypto.randomBytes(4).toString('hex')}`;
                const clientSecret = crypto.randomBytes(32).toString('hex');

                // Mapping vào tenant_application
                await this.tenantApplicationRepo.save({
                    tenantId: tenant.id,
                    applicationId: app.id,
                    clientId,
                    clientSecret,
                    redirectUris: appReq.redirectUris,
                    isActive: true,
                    plan: 'default',
                });
                appResults.push({
                    appCode: app.code,
                    appName: app.name,
                    clientId,
                    clientSecret,
                    redirectUris: appReq.redirectUris,
                });
            }
        }

        // 5. Trả về kết quả
        return {
            tenant,
            account,
            applications: appResults,
        };
    }
}
