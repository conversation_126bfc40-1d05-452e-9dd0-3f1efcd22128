import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateTenantAppReq {
    @ApiProperty()
    @IsNotEmpty()
    code: string;

    @ApiProperty()
    @IsNotEmpty()
    redirectUris: string[];
}

export class RegisterTenantReq {
    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @ApiProperty()
    @IsNotEmpty()
    domain: string;

    @ApiProperty()
    @IsNotEmpty()
    rootUsername: string;

    @ApiProperty()
    @IsNotEmpty()
    rootPassword: string;

    @ApiProperty()
    @IsNotEmpty()
    rootFullName: string;

    @ApiPropertyOptional()
    applications?: CreateTenantAppReq[];
}
