import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import { AdminTenantService } from './tenant.service';
import { Body } from '@nestjs/common';
import { CreateTenantAppReq, RegisterTenantReq } from './dto';

@DefController('tenants')
export class AdminTenantController {
    constructor(private readonly adminTenantService: AdminTenantService) {}

    @DefPost('create-application', {
        summary: 'Create application',
    })
    createApplication() {
        return this.adminTenantService.createApplication();
    }

    @DefPost('register', {
        summary: 'Create tenant and make root account',
    })
    register(@Body() body: RegisterTenantReq) {
        return this.adminTenantService.register(body);
    }
}
