import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { PREFIX_MODULE } from '../config-module';
import { AuthService } from './auth/auth.service';
import { AuthController } from './auth/auth.controller';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
@ChildModule({
    prefix: PREFIX_MODULE.client,
    imports: [PrimaryRepoModule],
    providers: [AuthService],
    controllers: [AuthController],
})
export class ClientModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
