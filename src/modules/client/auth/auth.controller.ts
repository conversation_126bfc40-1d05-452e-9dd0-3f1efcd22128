import { <PERSON>, Get, Post, Req, Res, Body, Render, Query } from '@nestjs/common';
import { AuthService } from './auth.service';
import { Response, Request } from 'express';

@Controller('auth')
export class AuthController {
    constructor(private readonly authService: AuthService) {}

    @Get('login')
    @Render('login')
    renderLogin(@Query() query: any) {
        return {
            redirectUri: query.redirectUri,
            clientId: query.clientId,
            state: query.state,
            error: query.error,
        };
    }

    @Post('login')
    async doLogin(@Body() body: any, @Res() res: Response) {
        const { username, password, clientId, redirectUri, state } = body;
        const code = await this.authService.validateLoginAndGenerateCode(
            username,
            password,
            clientId,
            redirectUri,
        );

        if (!code) {
            return res.render('login', {
                redirectUri,
                clientId,
                state,
                error: '<PERSON> tên đăng nhập hoặc mật khẩu!',
            });
        }

        const url = new URL(redirectUri);
        url.searchParams.set('code', code);
        if (state) url.searchParams.set('state', state);
        return res.redirect(url.toString());
    }

    @Post('token')
    async token(@Body() body: any) {
        const { code, clientId, clientSecret, redirectUri } = body;
        return this.authService.exchangeCodeForToken(code, clientId, clientSecret, redirectUri);
    }
}
