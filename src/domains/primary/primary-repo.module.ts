import { Module } from '@nestjs/common';
import { join } from 'path';
import { DefRepositoryModule } from 'nestjs-typeorm3-kit';
import { PRIMARY_CONNECTION_NAME } from '~/databases/constants';
@Module({
  imports: [
    DefRepositoryModule.forRootAsync({
      useFactory: () => ({
        globPattern: join(__dirname, '../**/*.repo.{ts,js}'),
        dataSource: PRIMARY_CONNECTION_NAME,
      }),
    }),
  ],
  exports: [DefRepositoryModule],
})
export class PrimaryRepoModule {}
