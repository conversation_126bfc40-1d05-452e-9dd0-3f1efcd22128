import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('token')
export class TokenEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  accountId: string;

  @Column({ length: 255 })
  clientId: string;

  @Column({ length: 500 })
  accessToken: string;

  @Column({ length: 500 })
  refreshToken: string;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @Column({ type: 'text', array: true, default: '{}' })
  scope: string[];
}
