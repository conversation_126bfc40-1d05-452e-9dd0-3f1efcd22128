import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('session')
export class SessionEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  accountId: string;

  @Column({ length: 255 })
  userAgent: string;

  @Column({ length: 255 })
  ip: string;

  @Column({ type: 'timestamp' })
  loginAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  logoutAt?: Date;
}
